package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Grupo;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioCargado;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.text.NumberFormat;
import java.util.ResourceBundle;
import java.util.Set;

/**
 * Controlador para la vista de un BienServicioCargado en el ListView de SaleController.
 * Cada celda del ListView representa un item cargado en la venta.
 */
@Component
@Scope("prototype")
@Slf4j
public class BienServicioCargadoController extends BaseController {

    @FXML
    private AnchorPane anchorPrecioCantidad;

    @FXML
    private Label cantidadXPrecioAcordado;

    @FXML
    private Label lblCantidad;

    @FXML
    private Label lblCodCompuesto;

    @FXML
    private Label lblDescripcion;

    @FXML
    private Label lblGrupoItem;

    @FXML
    private Label lblMarca;

    @FXML
    private Label lblPrecioAcordado;

    @FXML
    private AnchorPane rootBienServicioCargado;

    @FXML
    private VBox vbDatosItem;

    private NumberFormat decimalFormat;

    /**
     * Inicializa el formato decimal para mostrar precios sin el símbolo de moneda.
     */
    private void initializeDecimalFormat() {
        decimalFormat = NumberFormat.getNumberInstance();
        decimalFormat.setMinimumFractionDigits(2);
        decimalFormat.setMaximumFractionDigits(2);
    }
    private BienServicioCargado bienServicioCargado;

    /**
     * Configura el controlador con un BienServicioCargado y actualiza la UI.
     * @param bienServicioCargado El BienServicioCargado a mostrar
     */
    public void setBienServicioCargado(BienServicioCargado bienServicioCargado) {
        this.bienServicioCargado = bienServicioCargado;
        updateUI();
    }

    /**
     * Actualiza la interfaz de usuario con los datos del BienServicioCargado.
     */
    private void updateUI() {
        if (bienServicioCargado == null) {
            log.warn("No se puede actualizar UI: bienServicioCargado es null");
            return;
        }

        // Aplicar estilo según el stock
        applyStockStyling();

        // Mostrar código compuesto
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getCodCompuesto() != null) {
            lblCodCompuesto.setText(bienServicioCargado.getItem().getCodCompuesto());
        } else {
            lblCodCompuesto.setText("N/A");
        }

        // Mostrar marca
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getMarca() != null) {
            lblMarca.setText(bienServicioCargado.getItem().getMarca().getNombre());
        } else {
            lblMarca.setText("");
        }

        // Mostrar grupo del item
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getProductos() != null) {
            // Intentar obtener el grupo del primer producto asociado al item
            bienServicioCargado.getItem().getProductos().stream()
                    .findFirst()
                    .ifPresent(producto -> {
                        Set<Grupo> grupos = producto.getGrupos();
                        if (grupos != null && !grupos.isEmpty()) {
                            grupos.stream().findFirst().ifPresent(grupo ->
                                lblGrupoItem.setText(grupo.getNombrePrincipal())
                            );
                        } else {
                            lblGrupoItem.setText("");
                        }
                    });
        } else {
            lblGrupoItem.setText("");
        }

        // Mostrar descripción en Camel Case
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getDescripcion() != null) {
            String descripcion = bienServicioCargado.getItem().getDescripcion();
            lblDescripcion.setText(toCamelCase(descripcion));
        } else {
            lblDescripcion.setText("");
        }

        // Mostrar precio acordado sin símbolo de moneda
        if (bienServicioCargado.getPrecioAcordado() != null) {
            lblPrecioAcordado.setText(decimalFormat.format(bienServicioCargado.getPrecioAcordado()));
        } else if (bienServicioCargado.getPrecioInicial() != null) {
            // Si no hay precio acordado, usar el precio inicial
            lblPrecioAcordado.setText(decimalFormat.format(bienServicioCargado.getPrecioInicial()));
        } else {
            lblPrecioAcordado.setText(decimalFormat.format(0));
        }

        // Mostrar cantidad con ceros adelante (formato 3 dígitos)
        if (bienServicioCargado.getCantidad() != null) {
            int cantidad = bienServicioCargado.getCantidad().intValue();
            lblCantidad.setText(String.format("%03d", cantidad));
        } else {
            lblCantidad.setText("000");
        }

        // Mostrar total (cantidad x precio) sin símbolo de moneda
        if (bienServicioCargado.getMontoAcordado() != null) {
            cantidadXPrecioAcordado.setText(decimalFormat.format(bienServicioCargado.getMontoAcordado()));
        } else {
            // Calcular el total si no está disponible
            double precio = bienServicioCargado.getPrecioAcordado() != null ?
                    bienServicioCargado.getPrecioAcordado() :
                    (bienServicioCargado.getPrecioInicial() != null ? bienServicioCargado.getPrecioInicial() : 0);
            double cantidad = bienServicioCargado.getCantidad() != null ? bienServicioCargado.getCantidad() : 0;
            cantidadXPrecioAcordado.setText(decimalFormat.format(precio * cantidad));
        }
    }

    /**
     * Aplica estilos según el nivel de stock del item.
     * Items con stock cero o negativo tendrán un fondo de color de error.
     */
    private void applyStockStyling() {
        // Limpiar estilos previos
        rootBienServicioCargado.getStyleClass().removeAll("stock-error");

        // Verificar si el item tiene stock cero o negativo
        if (bienServicioCargado.getItem() != null &&
            bienServicioCargado.getItem().getStockTotal() != null &&
            bienServicioCargado.getItem().getStockTotal() <= 0) {
            // Aplicar estilo de error para stock cero o negativo
            rootBienServicioCargado.getStyleClass().add("stock-error");
        }
    }

    /**
     * Convierte un texto a formato Camel Case.
     * Ejemplo: "RODAJE DEL PROBOX" -> "Rodaje Del Probox"
     *
     * @param text El texto a convertir
     * @return El texto en formato Camel Case
     */
    private String toCamelCase(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // Dividir el texto en palabras
        String[] words = text.toLowerCase().split("\\s+");
        StringBuilder camelCase = new StringBuilder();

        // Convertir cada palabra a Camel Case
        for (String word : words) {
            if (!word.isEmpty()) {
                camelCase.append(Character.toUpperCase(word.charAt(0)))
                         .append(word.substring(1))
                         .append(" ");
            }
        }

        // Eliminar el espacio final y devolver el resultado
        return camelCase.toString().trim();
    }

    /**
     * Método de inicialización llamado por JavaFX después de que todos los elementos @FXML
     * han sido inyectados. Implementación requerida por la interfaz Initializable.
     *
     * @param url La ubicación utilizada para resolver rutas relativas para el objeto raíz
     * @param resourceBundle Los recursos utilizados para localizar el objeto raíz
     */
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Inicializar el formato decimal
        initializeDecimalFormat();

        // Configurar el comportamiento responsivo de los elementos
        configureResponsiveLayout();

//        // Asegurar que los tamaños de fuente sean consistentes
//        fixFontSizes();

        log.debug("BienServicioCargadoController inicializado");
    }

    /**
     * Configura el layout para que sea responsivo cuando cambia el tamaño disponible.
     * Asegura que la descripción se ajuste correctamente y que los elementos de precio
     * y cantidad siempre sean visibles.
     */
    private void configureResponsiveLayout() {


        // Asegurar que el AnchorPane padre ajuste correctamente sus hijos
        rootBienServicioCargado.widthProperty().addListener((obs, oldVal, newVal) -> {
            double availableWidth = newVal.doubleValue();
            double leftPanelWidth = vbDatosItem.getWidth();
            double rightPanelWidth = anchorPrecioCantidad.getWidth();

            // Calcular el espacio disponible para la descripción
            double descriptionWidth = availableWidth - leftPanelWidth - rightPanelWidth;

            // Ajustar el ancho máximo de la descripción si es necesario
            if (descriptionWidth > 0) {
                // Dejar un pequeño margen para evitar solapamientos
                lblDescripcion.setPrefWidth(descriptionWidth - 10);
            }
        });

        // Aplicar estilo de tarjeta
        rootBienServicioCargado.getStyleClass().add("card-style");
    }
}
