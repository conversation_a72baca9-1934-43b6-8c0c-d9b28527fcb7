<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.SplitPane?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<StackPane fx:id="stackPaneSale" stylesheets="@../../css/styles.css, @../../css/sale.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.SaleController">
   <children>
      <AnchorPane fx:id="anchorSale" maxWidth="630.0" minHeight="0.0" minWidth="300.0">
         <children>
            <HBox AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <CustomTextField fx:id="txtDocumentoNombreRazon" disable="true" editable="false" maxHeight="36.0" minHeight="36.0" promptText="(Documento) (? Nombre o Razón)" HBox.hgrow="ALWAYS">
                     <font>
                        <Font name="System Bold" size="15.0" />
                     </font>
                  </CustomTextField>
               </children>
            </HBox>
            <VBox fx:id="vbTotalVenta" alignment="CENTER" layoutX="4.0" layoutY="33.0" prefHeight="77.0" prefWidth="173.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="36.0">
               <children>
                  <AnchorPane>
                     <children>
                        <Label layoutX="92.0" text="TOTAL:" AnchorPane.rightAnchor="72.0">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblTotalMontoInicial" layoutX="64.0" text="9999.99" textAlignment="RIGHT" AnchorPane.rightAnchor="3.0">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                     </children>
                  </AnchorPane>
                  <AnchorPane>
                     <children>
                        <Label text="DESCUENTO:" AnchorPane.rightAnchor="72.0">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblDescuento" layoutX="107.0" text="1.99" textAlignment="RIGHT" AnchorPane.rightAnchor="3.0">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                     </children>
                  </AnchorPane>
                  <AnchorPane>
                     <children>
                        <Label>
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblTotalMontoAcordado" layoutX="9.0" text="9998.00" textAlignment="RIGHT" AnchorPane.rightAnchor="3.0">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                     </children>
                  </AnchorPane>
               </children>
            </VBox>
            <TextField fx:id="txtCodigo" alignment="CENTER" layoutX="182.0" layoutY="39.0" maxHeight="63.0" maxWidth="221.0" minHeight="63.0" minWidth="180.0" prefHeight="63.0" prefWidth="213.0" promptText="Código de Item" AnchorPane.leftAnchor="182.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="42.0">
               <font>
                  <Font name="System Bold" size="12.0" />
               </font>
            </TextField>
            <ListView fx:id="bienServicioCargados" styleClass="sale-list-view" fixedCellSize="70.0" layoutX="-5.0" layoutY="106.0" AnchorPane.bottomAnchor="45.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="111.0" />
            <AnchorPane fx:id="anchorBotones" prefHeight="45.0" prefWidth="450.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0">
               <children>
                  <HBox layoutX="22.0" layoutY="10.0" spacing="3.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                     <padding>
                        <Insets bottom="3.0" left="3.0" right="3.0" top="3.0" />
                     </padding>
                     <children>
                        <Button fx:id="btnLimpiar" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" text="Limpiar" HBox.hgrow="ALWAYS">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Button>
                        <Button fx:id="btnImprimir" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" text="Proforma" HBox.hgrow="ALWAYS">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Button>
                        <Button fx:id="btnCalcular" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" text="Calcular" HBox.hgrow="ALWAYS">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Button>
                        <Button fx:id="btnVender" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" text="Vender" HBox.hgrow="ALWAYS">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Button>
                     </children>
                  </HBox>
               </children>
            </AnchorPane>
         </children>
      </AnchorPane>
      <ProgressIndicator fx:id="loadingIndicator" maxHeight="100" maxWidth="100" visible="false" />
   </children>
</StackPane>
