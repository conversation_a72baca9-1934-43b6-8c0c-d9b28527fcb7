<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<AnchorPane fx:id="rootBienServicioCargado" minHeight="65.0" prefHeight="65.0" styleClass="bien-servicio-cargado" stylesheets="@../../css/sale.css" onMouseClicked="#handleClick" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.BienServicioCargadoController">
   <children>
      <!-- Contenido básico siempre visible -->
      <AnchorPane fx:id="anchorBasicContent" minHeight="65.0" prefHeight="65.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <VBox fx:id="vbDatosItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="lblGrupoItem" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefWidth="88.0" text="Rodaje" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="12.0" />
                     </font>
                     <VBox.margin>
                        <Insets top="3.0" />
                     </VBox.margin>
                  </Label>
                  <Label fx:id="lblCodCompuesto" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefHeight="45.0" prefWidth="88.0" styleClass="bien-servicio-codigo" text="00158KOY" textAlignment="CENTER" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <Label fx:id="lblMarca" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefWidth="88.0" styleClass="marca-label" text="Koyo" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="12.0" />
                     </font>
                     <VBox.margin>
                        <Insets bottom="3.0" />
                     </VBox.margin>
                  </Label>
               </children>
            </VBox>
            <!-- Usar un StackPane para superponer Label y CustomTextField -->
            <StackPane fx:id="stackDescripcion" layoutX="90.0" layoutY="0.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="90.0" AnchorPane.rightAnchor="90.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="lblDescripcionDisplay" alignment="CENTER" maxWidth="Infinity" styleClass="bien-servicio-descripcion" text="Descripcion Producto" wrapText="true" StackPane.alignment="CENTER">
                     <font>
                        <Font size="14.0" />
                     </font>
                     <padding>
                        <Insets left="3.0" right="3.0" />
                     </padding>
                  </Label>
                  <CustomTextField fx:id="descripcionDelBienServicio" maxWidth="Infinity" styleClass="bien-servicio-descripcion, label-like" text="Descripcion Producto" visible="false" StackPane.alignment="CENTER">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </CustomTextField>
               </children>
            </StackPane>
            <AnchorPane fx:id="anchorPrecioCantidad" layoutX="503.0" maxHeight="65.0" maxWidth="90.0" minHeight="65.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <CustomTextField fx:id="precioAcordado" alignment="CENTER" layoutY="1.0" styleClass="bien-servicio-precio, label-like" text="3.00" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="33.0" AnchorPane.topAnchor="0.0">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </CustomTextField>
                  <CustomTextField fx:id="cantidad" alignment="CENTER" layoutX="58.0" prefHeight="24.0" prefWidth="41.0" styleClass="bien-servicio-cantidad, label-like" text="001" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="58.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </CustomTextField>
                  <CustomTextField fx:id="montoAcordado" alignment="CENTER" styleClass="bien-servicio-total, label-like" text="3.00" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="24.0">
                     <font>
                        <Font size="21.0" />
                     </font>
                  </CustomTextField>
               </children>
            </AnchorPane>
         </children>
      </AnchorPane>

      <!-- Panel expandido que se muestra/oculta al hacer clic -->
      <VBox fx:id="vbExpandedContent" managed="false" visible="false" styleClass="expanded-content" spacing="8.0">
         <children>
            <!-- Fila superior: Precio Inicial y Precio Venta Base -->
            <VBox spacing="4.0">
               <children>
                  <Label text="Información de Precios" styleClass="section-header">
                     <font>
                        <Font name="System Bold" size="11.0" />
                     </font>
                  </Label>
                  <VBox spacing="2.0">
                     <children>
                        <Label fx:id="lblPrecioInicial" text="Precio Inicial: 0.00" styleClass="detail-label">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblPrecioVentaBase" text="Precio Venta Base: 0.00" styleClass="detail-label">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
               </children>
            </VBox>

            <!-- Fila inferior: Información del vehículo y códigos -->
            <VBox spacing="4.0">
               <children>
                  <Label text="Información del Item" styleClass="section-header">
                     <font>
                        <Font name="System Bold" size="11.0" />
                     </font>
                  </Label>
                  <VBox spacing="2.0">
                     <children>
                        <Label fx:id="lblVehiculo" text="Vehículo: N/A" styleClass="detail-label" wrapText="true">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblCodigosFabrica" text="Códigos: N/A" styleClass="detail-label" wrapText="true">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblUbicaciones" text="Ubicaciones: N/A" styleClass="detail-label" wrapText="true">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="8.0" left="10.0" right="10.0" top="8.0" />
         </padding>
      </VBox>
   </children>
</AnchorPane>
