<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<AnchorPane fx:id="rootBienServicioCargado" minHeight="65.0" prefHeight="65.0" styleClass="bien-servicio-cargado" stylesheets="@../../css/sale.css" onMouseClicked="#handleClick" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.BienServicioCargadoController">
   <children>
      <!-- Contenido básico siempre visible -->
      <AnchorPane fx:id="anchorBasicContent" minHeight="65.0" prefHeight="65.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <VBox fx:id="vbDatosItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="lblGrupoItem" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefWidth="88.0" text="Rodaje" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="12.0" />
                     </font>
                     <VBox.margin>
                        <Insets top="3.0" />
                     </VBox.margin>
                  </Label>
                  <Label fx:id="lblCodCompuesto" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefHeight="45.0" prefWidth="88.0" styleClass="bien-servicio-codigo" text="00158KOY" textAlignment="CENTER" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <Label fx:id="lblMarca" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefWidth="88.0" styleClass="marca-label" text="Koyo" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="12.0" />
                     </font>
                     <VBox.margin>
                        <Insets bottom="3.0" />
                     </VBox.margin>
                  </Label>
               </children>
            </VBox>
            <CustomTextField fx:id="descripcionDelBienServicio" layoutX="90.0" layoutY="23.0" maxWidth="Infinity" styleClass="bien-servicio-descripcion, label-like" text="Descripcion Producto" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="90.0" AnchorPane.rightAnchor="90.0" AnchorPane.topAnchor="0.0">
               <font>
                  <Font size="14.0" />
               </font>
            </CustomTextField>
            <AnchorPane fx:id="anchorPrecioCantidad" layoutX="503.0" maxHeight="65.0" maxWidth="90.0" minHeight="65.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <CustomTextField fx:id="precioAcordado" alignment="CENTER" layoutY="1.0" styleClass="bien-servicio-precio, label-like" text="3.00" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="33.0" AnchorPane.topAnchor="0.0">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </CustomTextField>
                  <CustomTextField fx:id="cantidad" alignment="CENTER" layoutX="58.0" prefHeight="24.0" prefWidth="41.0" styleClass="bien-servicio-cantidad, label-like" text="001" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="58.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </CustomTextField>
                  <CustomTextField fx:id="montoAcordado" alignment="CENTER" styleClass="bien-servicio-total, label-like" text="3.00" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="24.0">
                     <font>
                        <Font size="21.0" />
                     </font>
                  </CustomTextField>
               </children>
            </AnchorPane>
         </children>
      </AnchorPane>

      <!-- Panel expandido que se muestra/oculta al hacer clic -->
      <VBox fx:id="vbExpandedContent" managed="false" visible="false" styleClass="expanded-content">
         <children>
            <AnchorPane fx:id="anchorExpandedDetails" minHeight="80.0" prefHeight="80.0">
               <children>
                  <!-- Información del Item -->
                  <VBox fx:id="vbItemDetails" layoutX="10.0" layoutY="5.0" spacing="3.0" AnchorPane.leftAnchor="10.0" AnchorPane.topAnchor="5.0">
                     <children>
                        <Label text="Datos del Item:" styleClass="section-header">
                           <font>
                              <Font name="System Bold" size="11.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblPrecioInicial" text="Precio Inicial: 0.00">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblVehiculo" text="Vehículo: N/A">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblPrecioVentaBase" text="Precio Venta Base: 0.00">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>

                  <!-- Códigos de Fábrica -->
                  <VBox fx:id="vbCodigosFabrica" layoutX="200.0" layoutY="5.0" spacing="3.0" AnchorPane.leftAnchor="200.0" AnchorPane.topAnchor="5.0">
                     <children>
                        <Label text="Códigos de Fábrica:" styleClass="section-header">
                           <font>
                              <Font name="System Bold" size="11.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblCodigosFabrica" text="N/A" wrapText="true" maxWidth="150.0">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>

                  <!-- Ubicaciones -->
                  <VBox fx:id="vbUbicaciones" layoutX="400.0" layoutY="5.0" spacing="3.0" AnchorPane.rightAnchor="10.0" AnchorPane.topAnchor="5.0">
                     <children>
                        <Label text="Ubicaciones:" styleClass="section-header">
                           <font>
                              <Font name="System Bold" size="11.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblUbicaciones" text="N/A" wrapText="true" maxWidth="150.0">
                           <font>
                              <Font size="10.0" />
                           </font>
                        </Label>
                     </children>
                  </VBox>
               </children>
            </AnchorPane>
         </children>
      </VBox>
   </children>
</AnchorPane>
